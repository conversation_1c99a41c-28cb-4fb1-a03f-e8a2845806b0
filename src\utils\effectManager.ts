// 创建和销毁(静态特效的更新)在本文件中通过websocket执行  绑定到实体上面的特效的更新通过实体位置的移动执行

// import * as Cesium from 'cesium'
import { getStore } from './store-utils'
// 特效类型定义
export type EffectType =
  | 0 //'communicationLine' // 通信线效果
  | 1 //'scan' // 扫描\探测
  | 2 // 'fire' // 交火
  | 3 // 'tadiga' // 干扰
  | 4 // 'renderExplosionEffect' // 爆炸效果
  | 5 // '' // 平台日志
  | 6 // 'explosion' // 传感器信息
// | 'trail' // 轨迹效果
// 探测、通信、任务、开火、干扰线
// 特效配置接口
export interface EffectConfig {
  type: EffectType
  position?: {
    lo: number // 经度
    la: number // 纬度
    al: number // 高度
  }
  modelId: string // 绑定的模型ID
  target?: string // 目标模型ID（用于通信线）
  offset?: {
    // 相对于模型的偏移
    x: number
    y: number
    z: number
  }
  color?: Cesium.Color // 特效颜色
  customParams?: any // 自定义参数
  state: 0 | 1 // 0 创建 1销毁
}

// 特效实例接口
interface EffectInstance {
  id: string
  type: EffectType
  entity?: Cesium.Entity
  primitive?: Cesium.Primitive
  graphic?: any
  config: EffectConfig
  position: number[]
  show: boolean
}

export class EffectManager {
  private viewer: Cesium.Viewer
  private effects: Map<string, EffectInstance>
  private ws: WebSocket | null = null
  private wsUrl: string
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 5
  private reconnectDelay: number = 3000
  private effectCounter: number = 0
  translation: any
  rotation: any

  private hpr: Cesium.HeadingPitchRoll | undefined
  private particleSystem: any
  private trs: Cesium.TranslationRotationScale | undefined
  private renderUpdateHandler: (() => void) | null = null
  private viewModel: {
    heading?: number
    emissionRate: number
    gravity: number //设置重力参数
    minimumParticleLife: number
    maximumParticleLife: number
    minimumSpeed: number //粒子发射的最小速度
    maximumSpeed: number //粒子发射的最大速度
    startScale: number
    endScale: number
    particleSize: number
  }
  eventCallback: any
  group: GV.GraphicGroup
  constructor(viewer: Cesium.Viewer, wsUrl: string, eventCallback: any) {
    this.viewer = viewer
    this.wsUrl = wsUrl
    this.effects = new Map()
    this.eventCallback = eventCallback
    this.viewModel = {
      emissionRate: 5,
      gravity: 9.0, //设置重力参数
      minimumParticleLife: 1,
      maximumParticleLife: 12,
      minimumSpeed: 1.0, //粒子发射的最小速度
      maximumSpeed: 2.0, //粒子发射的最大速度
      startScale: 1.0,
      endScale: 5.0,
      particleSize: 25.0,
    }
    this.group = new GV.GraphicGroup()
    this.viewer.graphicLayer.add(this.group)
    this.translation = new Cesium.Cartesian3()
    this.rotation = new Cesium.Quaternion()
    this.hpr = new Cesium.HeadingPitchRoll()
    this.trs = new Cesium.TranslationRotationScale()
    this.entity = this.viewer.entities.add({
      //选择粒子放置的坐标
      // position: Cesium.Cartesian3.fromDegrees(this._longitude, this._latitude, this._altitude),
      position: Cesium.Cartesian3.fromDegrees(110, 28, 2600),
    })
    this.initWebSocket()

    // 添加场景渲染事件监听
    this.renderUpdateHandler = () => {
      this.updateEffects()
    }
    this.viewer.scene.preRender.addEventListener(this.renderUpdateHandler)
    this.registerPipelineMaterial() // 注册流水线材质
  }

  /**
   * 初始化WebSocket连接
   */
  private initWebSocket(): void {
    try {
      this.ws = new WebSocket(this.wsUrl)

      this.ws.onopen = () => {
        console.log('特效WebSocket连接已建立')
        this.reconnectAttempts = 0
      }

      this.ws.onmessage = event => {
        this.handleWebSocketMessage(event)
      }

      this.ws.onclose = () => {
        console.log('特效WebSocket连接已关闭')
        this.attemptReconnect()
      }

      this.ws.onerror = error => {
        console.error('特效WebSocket错误:', error)
      }
    } catch (error) {
      console.error('初始化特效WebSocket失败:', error)
      this.attemptReconnect()
    }
  }

  /**
   * 尝试重新连接WebSocket
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(
        `尝试重新连接特效WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`
      )

      setTimeout(() => {
        this.initWebSocket()
      }, this.reconnectDelay)
    } else {
      console.error('特效WebSocket重连失败，已达到最大重试次数')
    }
  }

  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data)
      this.batchProcessEffects(data)
    } catch (error) {
      console.error('处理特效WebSocket消息时出错:', error)
    }
  }

  /**
   * 批量处理特效
   */
  private batchProcessEffects(effectsData: any): void {
    if (!Array.isArray(effectsData)) {
      console.warn('批量处理特效需要数组格式')
      return
    }

    effectsData.forEach(
      (item: { type: EffectType; param: EffectConfig; name: string }) => {
        if (item.type !== 5) {
          if ('on_off' in item.param) {
            switch (item.param.on_off) {
              case true:
                this.createEffect(item.param, item.name, item.type)
                break
            }
          } else {
            switch (item.param.state) {
              case 0:
                this.createEffect(item.param, item.name, item.type)
                break
            }
          }
        } else {
          this.eventCallback({ data: item, time: effectsData.timestamp })
        }
      }
    )
  }

  /**
   * 创建特效
   */
  public createEffect(
    config: EffectConfig,
    name: string,
    type: EffectType
  ): string {
    // TODO 如果是双实体(例如通信a和b&a和c) 需要加上双实体的名称
    const id = `${name}${
      [0, 1, 2].includes(type) ? '_' + config.target : ''
    }_${type}`

    const effect: EffectInstance = {
      id,
      type,
      config: { ...config, modelId: name },
      position: [],
      show: this.readEffectsDisplayConfig(name, type),
    }
    if (!name) {
      // 如果没有绑定模型，使用配置中的位置
      const position = Cesium.Cartesian3.fromDegrees(
        effect.config.position?.lo || 0,
        effect.config.position?.la || 0,
        effect.config.position?.al || 0
      )
    } else {
      // 从实体集合中获取模型
      const model = this.viewer.entities.getById(name)
      if (!model || !model.position) {
        console.warn(
          `Model ${effect.config.modelId} not found or has no position for effect ${effect.id}`
        )
        return 'false'
      }

      // 获取模型位置并应用偏移
      const modelPosition = model.position.getValue(Cesium.JulianDate.now())
      if (!modelPosition) {
        console.warn(
          `Could not get position for model ${effect.config.modelId}`
        )
        return 'false'
      }

      const offset = effect.config.offset || { x: 0, y: 0, z: 0 }
      const position = new Cesium.Cartesian3(
        modelPosition.x + offset.x,
        modelPosition.y + offset.y,
        modelPosition.z + offset.z
      )
      effect.position = modelPosition
      this.effects.set(id, effect)
      this.renderEffect(effect)
    }
    // }

    return id
  }

  /**
   * 更新特效
   */
  public updateEffect(id: string, config: Partial<EffectConfig>): boolean {
    const effect = this.effects.get(id)
    if (!effect) {
      console.warn(`尝试更新不存在的特效: ${id}`)
      return false
    }

    // 更新配置
    effect.config = { ...effect.config, ...config }

    // 重新渲染特效
    this.destroyEffect(id, false)
    this.renderEffect(effect)

    // 更新特效位置
    if (effect.entity) {
      if (!effect.config.modelId) {
        // 如果没有绑定模型，使用配置中的位置
        const position = Cesium.Cartesian3.fromDegrees(
          effect.config.position?.lo || 0,
          effect.config.position?.la || 0,
          effect.config.position?.al || 0
        )
        effect.entity.position = new Cesium.ConstantPositionProperty(position)
      } else {
        // 从实体集合中获取模型
        const model = this.viewer.entities.getById(effect.config.modelId)
        if (!model || !model.position) {
          console.warn(
            `Model ${effect.config.modelId} not found or has no position for effect ${effect.id}`
          )
          return false
        }

        // 获取模型位置并应用偏移
        const modelPosition = model.position.getValue(Cesium.JulianDate.now())
        if (!modelPosition) {
          console.warn(
            `Could not get position for model ${effect.config.modelId}`
          )
          return false
        }

        const offset = effect.config.offset || { x: 0, y: 0, z: 0 }
        const position = new Cesium.Cartesian3(
          modelPosition.x + offset.x,
          modelPosition.y + offset.y,
          modelPosition.z + offset.z
        )
        effect.entity.position = new Cesium.ConstantPositionProperty(position)
      }
    }

    return true
  }

  /**
   * 销毁特效
   */
  public destroyEffect(id: string, removeFromMap: boolean = true): boolean {
    const effect = this.effects.get(id)
    if (!effect) {
      console.warn(`尝试销毁不存在的特效: ${id}`)
      return false
    }

    // 移除实体或图元
    if (effect.entity) {
      try {
        this.viewer.entities.remove(effect.entity)
        effect.entity = undefined
      } catch (err) {
        console.log(err, '销毁出错')
      }
    }

    if (effect.primitive) {
      this.viewer.scene.primitives.remove(effect.primitive)
      effect.primitive = undefined
    }
    if (effect.graphic) {
      this.viewer.graphicLayer.remove(effect.graphic)
      effect.graphic = undefined
    }

    // 从Map中移除
    if (removeFromMap) {
      this.effects.delete(id)
    }

    return true
  }
  public destroyEffectByModelId(id: string): boolean {
    const prefix = id + '_'
    for (const [effectId, effect] of this.effects.entries()) {
      if (effectId.startsWith(prefix)) {
        // 移除实体或图元
        if (effect.entity) {
          try {
            this.viewer.entities.remove(effect.entity)
            effect.entity = undefined
          } catch (err) {
            console.log(err, '销毁出错')
          }
        }

        if (effect.primitive) {
          this.viewer.scene.primitives.remove(effect.primitive)
          effect.primitive = undefined
        }
        if (effect.graphic) {
          this.viewer.graphicLayer.remove(effect.graphic)
          effect.graphic = undefined
        }

        this.effects.delete(effectId)
      }
    }
    return true
  }
  /**
   * 渲染特效
   */
  private renderEffect(effect: EffectInstance): void {
    switch (effect.type) {
      case 0:
        this.renderCommunicationLineEffect(effect)
        break
      case 1:
        this.renderScanLineEffect(effect)
        break
      case 2:
        this.renderFireLineEffect(effect)
        break
      case 3:
        break
      case 6:
        this.scanEffect(effect, effect.position)
        break
      default:
        console.warn(`未知的特效类型: ${effect.type}`)
    }
  }

  /**
   * 渲染爆炸效果
   */
  renderExplosionEffect(effect: EffectInstance): void {
    // 创建爆炸粒子系统
    const model = this.viewer.entities.getById(effect.config.modelId)
    const particleSystem = this.viewer.scene.primitives.add(
      new Cesium.ParticleSystem({
        image: '/effects/fire11.png',
        startScale: 1.0,
        endScale: 3.0,
        minimumParticleLife: 0.5,
        maximumParticleLife: 1.5,
        speed: 10.0,
        lifetime: 1.0,
        imageSize: new Cesium.Cartesian2(20, 20),
        //主模型参数(位置)
        modelMatrix: this.computeModelMatrix(model, Cesium.JulianDate.now()),
        // 发射器参数
        emitter: new Cesium.SphereEmitter(5.0),
        emissionRate: 30.0,
        emitterModelMatrix: this.computeEmitterModelMatrix(),
        //颜色
        startColor: Cesium.Color.RED.withAlpha(0.8),
        endColor: Cesium.Color.YELLOW.withAlpha(0.0),
        loop: false,
        bursts: [
          // these burst will occasionally sync to create a multicolored effect
          new Cesium.ParticleBurst({
            time: 0.0,
            minimum: 80,
            maximum: 100,
          }),
        ],
      })
    )
    effect.primitive = particleSystem

    this.effects.set(effect.config.modelId, effect)
  }
  private computeModelMatrix(entity, time) {
    //获取位置
    var position = Cesium.Property.getValueOrUndefined(
      entity.position,
      time,
      new Cesium.Cartesian3()
    )
    if (!Cesium.defined(position)) {
      return undefined
    }
    //获取方向
    var modelMatrix
    var orientation = Cesium.Property.getValueOrUndefined(
      entity.orientation,
      time,
      new Cesium.Quaternion()
    )
    if (!Cesium.defined(orientation)) {
      modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(
        position,
        undefined,
        new Cesium.Matrix4()
      )
    } else {
      modelMatrix = Cesium.Matrix4.fromRotationTranslation(
        Cesium.Matrix3.fromQuaternion(orientation, new Cesium.Matrix3()),
        position,
        new Cesium.Matrix4()
      )
    }
    return modelMatrix
  }
  private computeEmitterModelMatrix() {
    this.hpr = Cesium.HeadingPitchRoll.fromDegrees(
      0.0,
      0.0,
      0.0,
      new Cesium.HeadingPitchRoll()
    )
    var trs = new Cesium.TranslationRotationScale()

    //以modelMatrix(飞机)中心为原点的坐标系的xyz轴位置偏移
    trs.translation = Cesium.Cartesian3.fromElements(
      2.5,
      3.5,
      1.0,
      new Cesium.Cartesian3()
    )
    trs.rotation = Cesium.Quaternion.fromHeadingPitchRoll(
      this.hpr,
      new Cesium.Quaternion()
    )
    return Cesium.Matrix4.fromTranslationRotationScale(
      trs,
      new Cesium.Matrix4()
    )
  }
  /**
   * 渲染扫描效果
   */
  private scanEffect(
    effect: EffectInstance,
    position: Cesium.Cartesian3
  ): void {
    if (effect.config.name !== 'geo_sensor') return
    const model = this.viewer.entities.getById(effect.config.modelId)
    const newPosition = this.cartesianToLonLatHeight(position)
    let sensor = new GV.SensorGraphic({
      showScanPlane: false, //是否显示扫描面
      color: `${(window as any).GVJ.URLS.sensorColor.color}`,
      lineColor: `${(window as any).GVJ.URLS.sensorColor.lineColor}`,
      scanPlaneColor: `${(window as any).GVJ.URLS.sensorColor.scanPlaneColor}`,
      position: new GV.GeoPoint(
        newPosition.lon,
        newPosition.lat,
        newPosition.height
      ),
      xHalfAngle: 180,
      yHalfAngle: 180,
      radius: 175940,
    })
    this.viewer.graphicLayer.add(sensor)
    this.viewer.clock.onTick.addEventListener(() => {
      const newPosition = this.cartesianToLonLatHeight(
        model.position.getValue(Cesium.JulianDate.now())
      )
      sensor.position = new GV.GeoPoint(
        newPosition.lon,
        newPosition.lat,
        newPosition.height
      )
    })
    // 保存特效引用
    effect.graphic = sensor
    effect.graphic.visible = effect.show
  }
  private getOrientations(entity) {
    const orientation = entity.orientation.getValue(
      this.viewer.clock.currentTime
    )
    const position = entity.position.getValue(this.viewer.clock.currentTime)

    if (!orientation || !position) return null

    // 获取变换矩阵：从 local frame 转为 ENU 世界坐标
    const transform = Cesium.Transforms.eastNorthUpToFixedFrame(position)
    const rotationMatrix = Cesium.Matrix3.fromQuaternion(orientation)

    // 将旋转矩阵从世界系转换为本地 ENU 系
    const localRotation = Cesium.Matrix4.multiplyByMatrix3(
      Cesium.Matrix4.inverseTransformation(transform, new Cesium.Matrix4()),
      rotationMatrix,
      new Cesium.Matrix3()
    )
    const quaternion = Cesium.Quaternion.fromRotationMatrix(localRotation)
    const hpr = Cesium.HeadingPitchRoll.fromQuaternion(quaternion)

    return {
      yaw: Cesium.Math.toDegrees(hpr.heading),
      pitch: Cesium.Math.toDegrees(hpr.pitch),
      roll: Cesium.Math.toDegrees(hpr.roll),
    }
  }

  /**
   * 渲染通信线效果
   */
  private renderCommunicationLineEffect(effect: EffectInstance): void {
    if (!effect.config.modelId || !effect.config.target) {
      console.error('通信线效果需要指定源模型和目标模型ID')
      return
    }

    // 获取源模型和目标模型
    const sourceModel = this.viewer.entities.getById(effect.config.modelId)
    const targetModel = this.viewer.entities.getById(effect.config.target)

    if (!sourceModel || !targetModel) {
      console.error('无法找到指定的模型')
      return
    }
    // 创建通信线实体
    const entity = this.viewer.entities.add({
      polyline: {
        positions: new Cesium.CallbackProperty(() => {
          if (
            !sourceModel ||
            !this.viewer.entities.contains(sourceModel) ||
            !targetModel ||
            !this.viewer.entities.contains(targetModel)
          ) {
            return []
          }
          const sourcePosition = sourceModel.position?.getValue(
            Cesium.JulianDate.now()
          )
          const targetPosition = targetModel.position?.getValue(
            Cesium.JulianDate.now()
          )

          if (!sourcePosition || !targetPosition) {
            return []
          }

          // 添加一些中间点使线条更平滑
          const positions = [sourcePosition]
          const midPoint = Cesium.Cartesian3.lerp(
            sourcePosition,
            targetPosition,
            0.5,
            new Cesium.Cartesian3()
          )

          // 在中间点添加一些高度偏移，使线条呈现弧形
          midPoint.z += 100.0 // 可以根据需要调整高度
          positions.push(midPoint)
          positions.push(targetPosition)

          return positions
        }, false),
        width: 2.0,
        show: effect.show,
        material: new Cesium.Material.PolylineTrailMaterialProperty({
          color: Cesium.Color.WHITE,
          image: '/image/comm_line.png',
          duration: 1000,
          repeat: 15,
        }),
      },
    })

    effect.entity = entity
  }
  private renderScanLineEffect(effect: EffectInstance): void {
    if (!effect.config.modelId || !effect.config.target) {
      console.error('通信线效果需要指定源模型和目标模型ID')
      return
    }

    // 获取源模型和目标模型
    const sourceModel = this.viewer.entities.getById(effect.config.modelId)
    const targetModel = this.viewer.entities.getById(effect.config.target)

    if (!sourceModel || !targetModel) {
      console.error('无法找到指定的模型')
      return
    }
    const entity = this.viewer.entities.add({
      polyline: {
        positions: new Cesium.CallbackProperty(() => {
          if (
            !sourceModel ||
            !this.viewer.entities.contains(sourceModel) ||
            !targetModel ||
            !this.viewer.entities.contains(targetModel)
          ) {
            return []
          }
          const sourcePosition = sourceModel.position?.getValue(
            Cesium.JulianDate.now()
          )
          const targetPosition = targetModel.position?.getValue(
            Cesium.JulianDate.now()
          )

          if (!sourcePosition || !targetPosition) {
            return []
          }

          // 添加一些中间点使线条更平滑
          const positions = [sourcePosition]
          const midPoint = Cesium.Cartesian3.lerp(
            sourcePosition,
            targetPosition,
            0.5,
            new Cesium.Cartesian3()
          )

          // 在中间点添加一些高度偏移，使线条呈现弧形
          midPoint.z += 100.0 // 可以根据需要调整高度
          positions.push(midPoint)
          positions.push(targetPosition)
          return positions
        }, false),
        width: 2.0,
        show: effect.show,
        material: new Cesium.Material.PolylineTrailMaterialProperty({
          color: Cesium.Color.WHITE,
          image: '/image/scan_line.png',
          duration: 1000,
          repeat: 15,
        }),
      },
    })
    effect.entity = entity
  }
  private renderFireLineEffect(effect: EffectInstance): void {
    if (!effect.config.modelId || !effect.config.target) {
      console.error('通信线效果需要指定源模型和目标模型ID')
      return
    }

    // 获取源模型和目标模型
    const sourceModel = this.viewer.entities.getById(effect.config.modelId)
    const targetModel = this.viewer.entities.getById(effect.config.target)

    if (!sourceModel || !targetModel) {
      console.error('无法找到指定的模型')
      return
    }
    const positions = () => {
      if (
        !sourceModel ||
        !this.viewer.entities.contains(sourceModel) ||
        !targetModel ||
        !this.viewer.entities.contains(targetModel)
      ) {
        return []
      }
      const sourcePosition = sourceModel.position?.getValue(
        Cesium.JulianDate.now()
      )
      const targetPosition = targetModel.position?.getValue(
        Cesium.JulianDate.now()
      )

      if (!sourcePosition || !targetPosition) {
        return []
      }

      // 添加一些中间点使线条更平滑
      const positions = [sourcePosition]
      const midPoint = Cesium.Cartesian3.lerp(
        sourcePosition,
        targetPosition,
        0.5,
        new Cesium.Cartesian3()
      )

      // 在中间点添加一些高度偏移，使线条呈现弧形
      midPoint.z += 100.0 // 可以根据需要调整高度
      positions.push(midPoint)
      positions.push(targetPosition)
      return positions
    }
    let attack = new GV.ParabolaLineGraphic({
      positions: positions(),
      lineStyle: new GV.DynamicStyle({
        color: '#FF0000',
        icon: GV.DynamicIconEnum.Attack,
        duration: 2000,
      }),
      width: 2, //线宽
      interpolation: false,
      step: 50,
    })
    this.viewer.graphicLayer.add(attack)
    effect.graphic = attack
    effect.graphic.visible = effect.show
    // const entity = this.viewer.entities.add({
    //   polyline: {
    //     positions: new Cesium.CallbackProperty(() => {
    //       if (
    //         !sourceModel ||
    //         !this.viewer.entities.contains(sourceModel) ||
    //         !targetModel ||
    //         !this.viewer.entities.contains(targetModel)
    //       ) {
    //         return []
    //       }
    //       const sourcePosition = sourceModel.position?.getValue(
    //         Cesium.JulianDate.now()
    //       )
    //       const targetPosition = targetModel.position?.getValue(
    //         Cesium.JulianDate.now()
    //       )

    //       if (!sourcePosition || !targetPosition) {
    //         return []
    //       }

    //       // 添加一些中间点使线条更平滑
    //       const positions = [sourcePosition]
    //       const midPoint = Cesium.Cartesian3.lerp(
    //         sourcePosition,
    //         targetPosition,
    //         0.5,
    //         new Cesium.Cartesian3()
    //       )

    //       // 在中间点添加一些高度偏移，使线条呈现弧形
    //       midPoint.z += 100.0 // 可以根据需要调整高度
    //       positions.push(midPoint)
    //       positions.push(targetPosition)
    //       return positions
    //     }, false),
    //     width: 2.0,
    //     show: effect.show,
    //     material: new Cesium.PolylineOutlineMaterialProperty({
    //       outlineWidth: 0.2,
    //       color: effect.config.color || Cesium.Color.RED.withAlpha(1.0),
    //       outlineColor: Cesium.Color.BLACK,
    //     }),
    //   },
    // })
    // effect.entity = entity
  }

  /**
   * 销毁所有特效
   */
  public destroyAllEffects(): void {
    this.effects.forEach((effect, id) => {
      this.destroyEffect(id)
    })
  }

  /**
   * 关闭WebSocket连接
   */
  public close(): void {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  /**
   * 更新所有活跃的特效
   */
  private updateEffects(): void {
    this.effects.forEach(effect => {
      if (effect.type === 0 && effect.entity) {
        // 获取源模型和目标模型
        const sourceModel = this.viewer.entities.getById(effect.config.modelId)
        const targetModel = this.viewer.entities.getById(effect.config.target)
        if (!sourceModel || !targetModel) {
          this.destroyEffect(effect.id)
          return
        }
      }
      if (effect.isActive && effect.primitive) {
        // 检查粒子系统是否需要更新
        const particleSystem =
          effect.primitive as unknown as Cesium.ParticleSystem
        if (particleSystem && !particleSystem.isDestroyed()) {
          // 更新粒子系统的位置和方向
          const position = effect.config.position
          if (position) {
            // 将经纬度高度转换为笛卡尔坐标
            const cartesian = Cesium.Cartesian3.fromDegrees(
              position.lo,
              position.la,
              position.al
            )
            particleSystem.modelMatrix =
              Cesium.Transforms.eastNorthUpToFixedFrame(cartesian)
          }
        }
      }
    })
  }
  private cartesianToLonLatHeight(cartesian: Cesium.Cartesian3): any {
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian)
    const lon = Cesium.Math.toDegrees(cartographic.longitude)
    const lat = Cesium.Math.toDegrees(cartographic.latitude)
    const height = cartographic.height
    return { lon, lat, height }
  }
  // 将方向向量转换为 水平角（Azimuth）+ 竖直角（Elevation）
  public cartesianToAngles(cartesian: Cesium.Cartesian3) {
    const magnitude = Cesium.Cartesian3.magnitude(cartesian)
    const unit = Cesium.Cartesian3.normalize(cartesian, new Cesium.Cartesian3())

    const elevation = Cesium.Math.toDegrees(Math.asin(unit.z)) // z / length
    const azimuth = Cesium.Math.toDegrees(Math.atan2(unit.y, unit.x)) // atan2(y, x)

    return { azimuth, elevation }
  }

  // 将水平+竖直角 转换为方向向量（球坐标转笛卡尔）
  public anglesToCartesian(horizontalDeg, verticalDeg) {
    const azimuthRad = Cesium.Math.toRadians(horizontalDeg)
    const elevationRad = Cesium.Math.toRadians(verticalDeg)

    const x = Math.cos(elevationRad) * Math.cos(azimuthRad)
    const y = Math.cos(elevationRad) * Math.sin(azimuthRad)
    const z = Math.sin(elevationRad)

    return new Cesium.Cartesian3(x, y, z)
  }
  /**
   * 销毁特效管理器
   */
  public destroy(): void {
    // 移除渲染事件监听
    if (this.renderUpdateHandler) {
      this.viewer.scene.preRender.removeEventListener(this.renderUpdateHandler)
      this.renderUpdateHandler = null
    }
    this.destroyAllEffects()
    this.effects.clear()
  }

  /** 读取特效显示/隐藏配置 */
  private readEffectsDisplayConfig(na: string, type: number) {
    const store = getStore()
    const si = window.viewer.entities.getById(na)?.properties?.si?.getValue()
    if (!si) return false
    const config = store.state.simulationConfig
    // 类型与全局开关的映射
    const typeMap: Record<number, keyof typeof config> = {
      0: 'communicationLine',
      1: 'probState',
      2: 'fireState',
      6: 'sensorState',
    }
    // 判断阵营是否勾选
    const sideChecked =
      si === 'red'
        ? config.redState === 'checked'
        : config.blueState === 'checked'
    if (!sideChecked) return false
    // 判断类型对应的全局开关
    const key = typeMap[type]
    if (key && config[key] === 'unchecked') return false
    return true
  }

  /** 注册流动线材质 */
  private registerPipelineMaterial() {
    if (!Cesium.Material.PolylineTrailType) {
      Cesium.Material.PolylineTrailType = 'PolylineTrail'
      Cesium.Material.PolylineTrailMaterialProperty = function (options) {
        this._definitionChanged = new Cesium.Event()
        this.color = options.color || Cesium.Color.WHITE
        this.image = options.image
        this.duration = options.duration || 1000
        this.repeat = options.repeat || 5 // 调小repeat，矩形更宽更稀疏
        this._time = Date.now()
      }
      Cesium.Material.PolylineTrailMaterialProperty.prototype.getType =
        function () {
          return Cesium.Material.PolylineTrailType
        }
      Cesium.Material.PolylineTrailMaterialProperty.prototype.getValue =
        function (time, result) {
          if (!result) result = {}
          // Cesium 1.50 没有 getValueOrClonedDefault，直接赋值
          result.color =
            typeof this.color === 'function' ? this.color(time) : this.color
          result.image = this.image
          result.time =
            ((Date.now() - this._time) % this.duration) / this.duration
          result.repeat = this.repeat
          return result
        }
      Cesium.Material.PolylineTrailMaterialProperty.prototype.equals =
        function (other) {
          return this === other
        }
      Object.defineProperties(
        Cesium.Material.PolylineTrailMaterialProperty.prototype,
        {
          isConstant: {
            get: function () {
              return false
            },
          },
          definitionChanged: {
            get: function () {
              return this._definitionChanged
            },
          },
        }
      )
      Cesium.Material.PolylineTrailSource = [
        'czm_material czm_getMaterial(czm_materialInput materialInput) {',
        '  czm_material material = czm_getDefaultMaterial(materialInput);',
        '  vec2 st = materialInput.st;',
        '  float t = fract(time + st.s);',
        '  vec4 colorImage = texture2D(image, vec2(fract(st.s * repeat - t), st.t));',
        '  material.alpha = colorImage.a * color.a;',
        '  material.diffuse = color.rgb * colorImage.rgb;',
        '  return material;',
        '}',
      ].join('\n')
      Cesium.Material._materialCache.addMaterial(
        Cesium.Material.PolylineTrailType,
        {
          fabric: {
            type: Cesium.Material.PolylineTrailType,
            uniforms: {
              color: new Cesium.Color(1.0, 1.0, 1.0, 1.0),
              image: '',
              time: 0,
              repeat: 0,
            },
            source: Cesium.Material.PolylineTrailSource,
          },
          translucent: function () {
            return true
          },
        }
      )
      window.Cesium.PolylineTrailMaterialProperty =
        Cesium.Material.PolylineTrailMaterialProperty
    }
  }
}
