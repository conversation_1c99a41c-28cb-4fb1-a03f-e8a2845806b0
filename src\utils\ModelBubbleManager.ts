// utils/ModelBubbleManager.ts
// 用于管理模型标牌的单例类

// @ts-ignore
const GV = (window as any).GV

export class ModelBubbleManager {
  private static readonly bindModelId: string = 'LABEL_BUBBLE'
  private bubble: any = null
  private frameId: number = 0
  /**
   * 显示标牌（如未创建则创建）
   */
  /**
   * 显示标牌（如未创建则创建）
   * @param entityId 需要绑定的模型id
   */
  public showBubble(entityId: string) {
    const bubbleId = ModelBubbleManager.bindModelId
    const graphicLayer = (window as any).viewer?.graphicLayer
    if (!graphicLayer) return
    // 查找是否已存在该标牌

    // 获取模型位置
    let position
    const viewer = (window as any).viewer
    if (viewer && typeof viewer.entities.getById === 'function' && entityId) {
      const entity = viewer.entities.getById(entityId)
      if (entity && entity.position) {
        const cartesian =
          typeof entity.position.getValue === 'function'
            ? entity.position.getValue(viewer.clock.currentTime)
            : entity.position
        if (cartesian && typeof Cesium !== 'undefined') {
          const cartographic = Cesium.Cartographic.fromCartesian(cartesian)
          const lon = Cesium.Math.toDegrees(cartographic.longitude)
          const lat = Cesium.Math.toDegrees(cartographic.latitude)
          const height = cartographic.height
          position = { lon, lat, height }
        }
      }
    }
    if (!position) position = { lon: 100, lat: 40, height: 0 }
    let bubble = graphicLayer.getById(bubbleId)
    if (bubble) {
      // 更新位置
      bubble.bindObject = new GV.GeoPoint(
        position.lon,
        position.lat,
        position.height
      )
      bubble.visible = true
      this.bubble = bubble
      this.updateData(entityId)
      return
    }
    // 没有则创建
    const bubbleGraphicOpt = new GV.BubbleGraphicOpt()
    bubbleGraphicOpt.id = bubbleId
    bubbleGraphicOpt.bindObject = new GV.GeoPoint(
      position.lon,
      position.lat,
      position.height
    )
    bubbleGraphicOpt.bubbleType = GV.BubbleTypeEnum.FixedBubble
    bubbleGraphicOpt.panelOffset = {
      x: 50,
      y: 50,
    }
    this.bubble = new GV.BubbleGraphic(bubbleGraphicOpt)
    graphicLayer.add(this.bubble)
    this.bubble.panelDom.innerHTML = `<div id="gv-bubble">
    <div class="title">
      <span>实体信息</span>
      <img id="bubble-close-btn" src="/image/close_icon.png" width="14" />
    </div>
    <div class="contentBox">
      <div class="item" id="e-name">
        <div class="label">
          <div class="iconBox">
             <img class="icon" width="15" src="/image/na_icon.png" alt="" />
          </div>
          <span>名称：</span>
          </div>
        <div class="value">红方战斗机</div>
      </div>
      <div class="item" id="e-si">
        <div class="label">
          <div class="iconBox">
            <img class="icon" width="15" src="/image/si_icon.png" alt="" />
          </div>
          <span>阵营：</span>
        </div>
        <div class="value"></div>
      </div>
      <div class="item" id="e-lon">
        <div class="label">
          <div class="iconBox">
            <img class="icon" width="15" src="/image/lon_icon.png" alt="" />
          </div>
          <span>经度：</span>
        </div>
        <div class="value"></div>
      </div>
      <div class="item" id="e-lat">
        <div class="label">
          <div class="iconBox">
            <img class="icon" width="15" src="/image/lat_icon.png" alt="" />
          </div>
          <span>纬度：</span>
        </div>
        <div class="value"></div>
      </div>
      <div class="item" id="e-alt">
        <div class="label">
          <div class="iconBox">
            <img class="icon" width="15" src="/image/alt_icon.png" alt="" />
          </div>
          <span>高度：</span>
        </div>
        <div class="value"></div>
      </div>
    </div>
    </div>`
    this.updateData(entityId)
    // 绑定关闭按钮事件
    const closeBtn = this.bubble.panelDom.querySelector('#bubble-close-btn')
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hideBubble())
    }
  }

  /** 隐藏标牌 */
  public hideBubble() {
    if (this.bubble) {
      this.bubble.visible = false
      cancelAnimationFrame(this.frameId)
    }
  }

  /** 更新数据 */
  updateData(entityId: string) {
    if (!this.bubble?.visible) return
    console.log('进进进')
    const entity = (window as any).viewer.entities.getById(entityId)
    if (!entity) return
    const properties = entity.properties
    if (!properties) return

    // 更新位置
    const position = entity.position.getValue(Cesium.JulianDate.now())
    if (position) {
      const cartographic = Cesium.Cartographic.fromCartesian(position)
      const lon = Cesium.Math.toDegrees(cartographic.longitude)
      const lat = Cesium.Math.toDegrees(cartographic.latitude)
      const height = cartographic.height
      this.bubble.bindObject = new GV.GeoPoint(lon, lat, height)
    }

    const name = properties.na.getValue()
    const side = properties.si.getValue()
    const lon = properties.lo.getValue()
    const lat = properties.la.getValue()
    const alt = properties.al.getValue()
    const nameEle = document
      .getElementById('e-name')
      ?.getElementsByClassName('value')[0]
    const sideEle = document
      .getElementById('e-si')
      ?.getElementsByClassName('value')[0]
    const lonEle = document
      .getElementById('e-lon')
      ?.getElementsByClassName('value')[0]
    const latEle = document
      .getElementById('e-lat')
      ?.getElementsByClassName('value')[0]
    const altEle = document
      .getElementById('e-alt')
      ?.getElementsByClassName('value')[0]
    if (nameEle) nameEle.innerHTML = name
    if (sideEle) sideEle.innerHTML = side
    if (lonEle) lonEle.innerHTML = `${lon.toFixed(5)}(度)`
    if (latEle) latEle.innerHTML = `${lat.toFixed(5)}(度)`
    if (altEle) altEle.innerHTML = `${alt.toFixed(2)}(米)`
    this.frameId = requestAnimationFrame(() => this.updateData(entityId))
  }
}

// 使用说明：
// 1. 在页面或业务代码中引入本类
// 2. 直接 new ModelBubbleManager()，然后调用 showBubble()、hideBubble()、setBubbleHtml(html) 方法
