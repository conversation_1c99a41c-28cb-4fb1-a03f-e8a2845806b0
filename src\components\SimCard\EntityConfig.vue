<template>
  <div class="entityConfigBox">
    <el-row class="title">
      <img src="/image/entityConfig_icon.png" alt="" class="icon" width="15" />
      <span>实体配置</span>
    </el-row>
    <div class="contentBox">
      <el-row class="row">
        <el-row class="col">
          <img src="/image/communication_icon.png" alt="" class="icon" />
          <span class="label"> 通讯 </span>
          <img
            class="selected"
            :src="
              entityConfigCommunication
                ? '/image/selected_icon.png'
                : '/image/unselected_icon.png'
            "
            alt=""
            width="15"
            @click="changeCommunication(!entityConfigCommunication)"
          />
        </el-row>
        <el-row class="col">
          <img src="/image/prob_icon.png" alt="" class="icon" />
          <span class="label">探测</span>
          <img
            class="selected"
            :src="
              entityConfigProb
                ? '/image/selected_icon.png'
                : '/image/unselected_icon.png'
            "
            alt=""
            width="15"
            @click="changeProb(!entityConfigProb)"
          />
        </el-row>
      </el-row>
      <el-row class="row">
        <el-row class="col">
          <img src="/image/fire_icon.png" alt="" class="icon" />
          <span class="label">交火</span>
          <img
            class="selected"
            :src="
              entityConfigFire
                ? '/image/selected_icon.png'
                : '/image/unselected_icon.png'
            "
            alt=""
            width="15"
            @click="changeFire(!entityConfigFire)"
          />
        </el-row>
        <el-row class="col">
          <img src="/image/trail_icon.png" alt="" class="icon" />
          <span class="label">尾迹</span>
          <img
            class="selected"
            :src="
              entityConfigTrail
                ? '/image/selected_icon.png'
                : '/image/unselected_icon.png'
            "
            alt=""
            width="15"
            @click="changeTrail(!entityConfigTrail)"
          />
        </el-row>
      </el-row>
      <el-row class="row">
        <el-row class="col">
          <img src="/image/label_icon.png" alt="" class="icon" />
          <span class="label">标牌</span>
          <img
            class="selected"
            :src="
              entityConfigLabel
                ? '/image/selected_icon.png'
                : '/image/unselected_icon.png'
            "
            alt=""
            width="15"
            @click="changeLabel(!entityConfigLabel)"
          />
        </el-row>
        <el-row class="col">
          <img src="/image/sensor_icon.png" alt="" class="icon" width="16" />
          <span class="label">雷达</span>
          <img
            class="selected"
            :src="
              entityConfigSensor
                ? '/image/selected_icon.png'
                : '/image/unselected_icon.png'
            "
            alt=""
            width="15"
            @click="changeSensor(!entityConfigSensor)"
          />
        </el-row>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, inject, computed } from 'vue'
import { useStore } from 'vuex'
const store = useStore()

const entityConfigCommunication = computed({
  get: () => store.state.simulationConfig.entityConfigCommunication,
  set: v => store.commit('SET_ENTITY_CONFIG_COMMUNICATION', v),
})
const entityConfigProb = computed({
  get: () => store.state.simulationConfig.entityConfigProb,
  set: v => store.commit('SET_ENTITY_CONFIG_PROB', v),
})
const entityConfigFire = computed({
  get: () => store.state.simulationConfig.entityConfigFire,
  set: v => store.commit('SET_ENTITY_CONFIG_FIRE', v),
})
const entityConfigTrail = computed({
  get: () => store.state.simulationConfig.entityConfigTrail,
  set: v => store.commit('SET_ENTITY_CONFIG_TRAIL', v),
})
const entityConfigLabel = computed({
  get: () => store.state.simulationConfig.entityConfigLabel,
  set: v => store.commit('SET_ENTITY_CONFIG_LABEL', v),
})
const entityConfigSensor = computed({
  get: () => store.state.simulationConfig.entityConfigSensor,
  set: v => store.commit('SET_ENTITY_CONFIG_SENSOR', v),
})

const currentEntityId = inject('currentEntityId', ref(''))

watch(
  () => currentEntityId.value,
  val => {
    if (!val) return
    let retry = 0
    const tryUpdate = () => {
      let found = false
      // 通讯
      if (getInitiativeEffects(val, 0).some(effect => effect.entity.show)) {
        entityConfigCommunication.value = true
        found = true
      } else {
        entityConfigCommunication.value = false
      }
      // 探测
      if (getInitiativeEffects(val, 1).some(effect => effect.entity.show)) {
        entityConfigProb.value = true
        found = true
      } else {
        entityConfigProb.value = false
      }
      // 交火
      if (getInitiativeEffects(val, 2).some(effect => effect.entity.show)) {
        entityConfigFire.value = true
        found = true
      } else {
        entityConfigFire.value = false
      }
      // 尾迹
      const trail = window.modelManager.trailPrimitive.trails.get(val)
      if (trail?.polyLine) {
        entityConfigTrail.value = trail.polyLine.show
        found = true
      } else {
        entityConfigTrail.value = false
      }
      // 标牌
      const labelEffect = window.viewer.entities.getById(val)
      if (labelEffect?.label) {
        entityConfigLabel.value = labelEffect.label.show?.getValue() || false
        found = true
      } else {
        entityConfigLabel.value = false
      }
      // 雷达
      const sensorEffect = (window as any).effectManager.effects.get(`${val}_6`)
      if (sensorEffect?.graphic) {
        entityConfigSensor.value = sensorEffect.graphic.visible
        found = true
      } else {
        entityConfigSensor.value = false
      }
      if (!found && retry < 3) {
        retry++
        setTimeout(tryUpdate, 200)
      }
    }
    tryUpdate()
  }
)
watch(
  () => store.state.simulationConfig.taskStatus,
  val => {
    if (val === 1) {
      entityConfigCommunication.value = true
      entityConfigProb.value = true
      entityConfigFire.value = true
      entityConfigTrail.value = true
      entityConfigLabel.value = true
      entityConfigSensor.value = true
    }
  }
)

// 改变通讯
const changeCommunication = (val: boolean) => {
  const effects = getInitiativeEffects(currentEntityId.value, 0)
  entityConfigCommunication.value = val
  if (!effects.length) return
  effects.forEach(effect => {
    effect.entity.show = val
  })
}
// 改变探测
const changeProb = (val: boolean) => {
  const effects = getInitiativeEffects(currentEntityId.value, 1)
  entityConfigProb.value = val
  if (!effects.length) return
  effects.forEach(effect => {
    effect.entity.show = val
  })
}
// 改变交火
const changeFire = (val: boolean) => {
  const effects = getInitiativeEffects(currentEntityId.value, 2)
  entityConfigFire.value = val
  if (!effects.length) return
  effects.forEach(effect => {
    effect.graphic.visible = val
  })
}

// 改变尾迹
const changeTrail = (val: boolean) => {
  const trail = window.modelManager.trailPrimitive.trails.get(
    currentEntityId.value
  )
  entityConfigTrail.value = val
  if (!trail) return
  trail.polyLine.show = val
}
// 改变标牌
const changeLabel = (val: boolean) => {
  const effect = window.viewer.entities.getById(currentEntityId.value)
  entityConfigLabel.value = val
  if (!effect) return
  effect.label.show.setValue(val)
}
// 改变雷达
const changeSensor = (val: boolean) => {
  const effect = (window as any).effectManager.effects.get(
    currentEntityId.value + '_' + '6'
  )
  entityConfigSensor.value = val
  if (!effect) return
  effect.graphic.visible = val
}

// 获取与当前实体主动发出的线
function getInitiativeEffects(entityId: string, type: number) {
  const effects = window.effectManager.effects
  const relatedCommunicationEffects: any = []
  effects.forEach((effect, key) => {
    // 通信线id格式为 a_b_0
    if (key.endsWith(`_${type}`) && key.split('_')[0] === entityId) {
      relatedCommunicationEffects.push(effect)
    }
  })
  return relatedCommunicationEffects
}
</script>

<style lang="less" scoped>
.entityConfigBox {
  height: 197px;
  position: absolute;
  width: 100%;
  bottom: 0;
  background-color: #002941;
  border: 1px solid var(--app-border-color);
  border-right: none;
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
  .title {
    height: 32px;
    align-items: center;
    background-image: linear-gradient(90deg, #1f77ad 0%, #002942 100%);
    padding-left: 9px;
    .icon {
      margin-right: 8px;
    }
  }
  .contentBox {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    height: calc(100% - 32px);
    padding: 0 10px;
    .row {
      .col {
        display: flex;
        align-items: center;
        justify-content: center;
        width: calc(50% - 5px);
        height: 36px;
        background: url('/image/params_bg.png') no-repeat 0 0 / 100% 100%;
        .icon {
          margin-right: 5px;
        }
        .label {
          margin-right: 15px;
          font-size: 15px;
        }
        .selected {
          cursor: pointer;
        }
      }
      .col:nth-child(odd) {
        margin-right: 10px;
      }
    }
  }
}
</style>
