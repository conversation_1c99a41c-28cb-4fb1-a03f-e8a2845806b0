.geoBubble {
  .geoBubblePanel {
    #gv-bubble {
      width: 190px;
      background-color: #002941;
      border: 1px solid var(--app-border-color);
      border-radius: 2px;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: 28px;
        background-image: linear-gradient(90deg, #1f77ad 0%, #002942 100%);
        padding: 0 9px;
        .icon {
          margin-right: 8px;
        }
      }
      .contentBox {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 5px 10px;
        .item {
          display: flex;
          align-items: center;
          height: 26px;
          .label {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            .iconBox {
              width: 20px;
              text-align: left;
              margin-top: -1px;
            }
          }
          .value {
            flex: 1;
            min-width: 0;
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
  .geoBubbleLine {
    border-color: var(--app-border-color) !important;
  }
}
